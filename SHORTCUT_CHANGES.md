# 快捷键功能实现与Bug修复说明

## 需求
实现按 Command+K（macOS）或 Ctrl+K（Windows）聚焦到搜索栏的功能，确保 Windows 与 macOS 兼容性。

## Bug修复记录

### Bug 1: Cmd+Shift+L / Ctrl+Shift+L 不能清除搜索内容
**问题原因**：`isShiftShortcut` 方法中的键值比较逻辑有误，当按下 Shift+L 时，`e.key` 可能是大写的 'L'。

**修复方案**：

- 修改 `isShiftShortcut` 方法，确保正确处理大小写键值比较
- 统一使用 `toLowerCase()` 进行比较

### Bug 2: 在 Spotlight 搜索中按 Cmd+K / Ctrl+K 依然会聚焦搜索栏

**问题原因**：全局键盘事件监听器没有检查 Spotlight 搜索状态，导致快捷键冲突。

**修复方案**：

- 在 `handleGlobalKeyboard` 方法中添加 Spotlight 搜索状态检查
- 当 Spotlight 搜索打开时，跳过全局快捷键处理，让 Spotlight 内部处理

## 实现的修改

### 1. 修改搜索管理器 (`nav/js/search.js`)

#### 新增功能
- **新增 `focusSearchInput()` 方法**：实现聚焦搜索栏功能
  - 聚焦主搜索输入框 `#searchInput`
  - 自动选中现有内容，方便用户直接输入新内容
  - 如果有搜索内容，自动显示搜索结果

#### 快捷键重新分配
- **Cmd+K / Ctrl+K**：从"清除搜索内容"改为"聚焦搜索栏"
- **Cmd+Shift+L / Ctrl+Shift+L**：新增，用于清除搜索内容
- **Cmd+Shift+K / Ctrl+Shift+K**：保持不变，清除所有筛选条件

#### Spotlight 搜索兼容性
- 在 Spotlight 搜索模式下，Cmd+K/Ctrl+K 仍然保持清除搜索框内容的行为
- 新增 Cmd+Shift+L/Ctrl+Shift+L 作为备用清除快捷键

### 2. 更新平台检测模块 (`nav/js/platform.js`)

#### 快捷键说明更新
- 更新 `getShortcutList()` 方法中的快捷键描述
- 更新搜索框占位符文本，显示正确的聚焦快捷键

#### 占位符文本优化
- 从 `"搜索网站名称、描述、标签... (空格键唤起搜索)"` 
- 改为 `"搜索网站名称、描述、标签... Cmd+K"` 或 `"搜索网站名称、描述、标签... Ctrl+K"`

## 快捷键功能总览

| 快捷键 | macOS | Windows | 功能描述 |
|--------|-------|---------|----------|
| 聚焦搜索栏 | ⌘+K | Ctrl+K | 聚焦主搜索输入框并选中内容 |
| 清除搜索内容 | ⌘+Shift+L | Ctrl+Shift+L | 清除搜索框中的内容 |
| 清除筛选条件 | ⌘+Shift+K | Ctrl+Shift+K | 清除所有搜索筛选条件 |
| 唤起搜索栏 | Space | Space | 显示 Spotlight 风格搜索覆盖层 |
| 关闭/清空 | ESC | ESC | 关闭搜索栏或清空搜索内容 |

## 兼容性保证

### 平台检测
- 使用 `Platform.isMac` 检测操作系统
- 自动适配 `metaKey`（macOS）和 `ctrlKey`（Windows）
- 动态显示正确的快捷键提示文本

### 事件处理
- 使用 `Platform.isShortcut(event, key)` 统一处理快捷键
- 支持 Shift 组合键检测 `isShiftShortcut(event, key)`
- 防止默认行为，避免浏览器快捷键冲突

## 测试方法

### 1. 基本功能测试
1. 打开导航页面
2. 按 Cmd+K（macOS）或 Ctrl+K（Windows）
3. 验证搜索框是否获得焦点并选中内容

### 2. 清除功能测试
1. 在搜索框中输入内容
2. 按 Cmd+Shift+L（macOS）或 Ctrl+Shift+L（Windows）
3. 验证搜索框内容是否被清除

### 3. 筛选清除测试
1. 设置一些搜索筛选条件
2. 按 Cmd+Shift+K（macOS）或 Ctrl+Shift+K（Windows）
3. 验证所有筛选条件是否被清除

### 4. Spotlight 搜索测试
1. 按空格键唤起 Spotlight 搜索
2. 在 Spotlight 搜索框中测试各种快捷键
3. 验证快捷键行为是否正确

## 使用测试页面

创建了 `test-shortcuts.html` 测试页面，可以：
- 检测当前操作系统和平台信息
- 测试快捷键功能是否正常工作
- 显示所有可用的快捷键列表
- 提供实时反馈和状态显示

访问 `http://localhost:8000/test-shortcuts.html` 进行测试。

## 注意事项

1. **向后兼容**：原有的空格键唤起搜索功能保持不变
2. **用户体验**：聚焦时自动选中内容，方便用户直接输入
3. **视觉反馈**：搜索框获得焦点时有明显的视觉提示
4. **错误处理**：如果搜索框不存在，会在控制台输出警告信息

## 实现细节

### 核心代码位置
- 主要逻辑：`nav/js/search.js` 第 950-969 行（`focusSearchInput` 方法）
- 快捷键处理：`nav/js/search.js` 第 199-203 行
- 平台检测：`nav/js/platform.js` 第 30-34 行

### 事件流程
1. 用户按下 Cmd+K 或 Ctrl+K
2. `handleGlobalKeyboard` 方法捕获事件
3. `Platform.isShortcut` 验证快捷键
4. 调用 `focusSearchInput` 方法
5. 搜索框获得焦点并选中内容
